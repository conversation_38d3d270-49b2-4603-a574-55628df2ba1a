Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB730, 0007FFFFA630) msys-2.0.dll+0x1FE8E
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210286019, 0007FFFFB5E8, 0007FFFFB730, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB730  000210068E24 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA10  00021006A225 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA403D0000 ntdll.dll
7FFA3F9E0000 KERNEL32.DLL
7FFA3D9E0000 KERNELBASE.dll
7FFA3F0B0000 USER32.dll
7FFA3D890000 win32u.dll
7FFA40360000 GDI32.dll
7FFA3D8C0000 gdi32full.dll
7FFA3E030000 msvcp_win.dll
7FFA3DDA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA3FDF0000 advapi32.dll
7FFA3E300000 msvcrt.dll
7FFA3FEB0000 sechost.dll
7FFA3E0D0000 bcrypt.dll
7FFA3FF90000 RPCRT4.dll
7FFA3CE80000 CRYPTBASE.DLL
7FFA3D810000 bcryptPrimitives.dll
7FFA3F790000 IMM32.DLL
