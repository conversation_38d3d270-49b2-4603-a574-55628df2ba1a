<?php

namespace App\Controllers;

class Spinner extends BaseController
{
    public function index()
    {
        return view('spinner/index');
    }

    public function spin()
    {
        $request = $this->request;
        
        // Ambil pilihan dari form
        $options = [];
        $optionInputs = $request->getPost('options');
        
        if ($optionInputs) {
            foreach ($optionInputs as $option) {
                $option = trim($option);
                if (!empty($option)) {
                    $options[] = $option;
                }
            }
        }
        
        // Jika tidak ada pilihan, gunakan default
        if (empty($options)) {
            $options = ['Pilihan 1', 'Pilihan 2', 'Pilihan 3', 'Pilihan 4'];
        }
        
        // Pilih secara random
        $selectedIndex = array_rand($options);
        $selectedOption = $options[$selectedIndex];
        
        return $this->response->setJSON([
            'success' => true,
            'result' => $selectedOption,
            'options' => $options,
            'selectedIndex' => $selectedIndex
        ]);
    }
}
