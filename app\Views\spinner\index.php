<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aplikasi Spinner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .spinner-container {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 30px auto;
        }

        .spinner {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: relative;
            overflow: hidden;
            border: 5px solid #333;
            transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
        }

        .spinner-segment {
            position: absolute;
            width: 50%;
            height: 50%;
            transform-origin: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            font-size: 14px;
            text-align: center;
            padding: 10px;
            word-wrap: break-word;
        }

        .spinner-pointer {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 30px solid #ff4757;
            z-index: 10;
        }

        .options-form {
            margin-bottom: 30px;
        }

        .option-input {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .option-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .add-option-btn {
            background: #2ed573;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: background 0.3s;
        }

        .add-option-btn:hover {
            background: #26d467;
        }

        .remove-option-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .spin-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .spin-btn:hover {
            transform: translateY(-2px);
        }

        .spin-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            text-align: center;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }

        .result.show {
            display: block;
        }

        .result h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .result-text {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .option-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .spinning {
            animation: spin 3s cubic-bezier(0.23, 1, 0.32, 1) forwards;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(1800deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Aplikasi Spinner</h1>
        
        <form id="optionsForm" class="options-form">
            <h3>Masukkan Pilihan Anda:</h3>
            <div id="optionsContainer">
                <div class="option-group">
                    <input type="text" class="option-input" name="options[]" placeholder="Pilihan 1" value="Pilihan 1">
                    <button type="button" class="remove-option-btn" onclick="removeOption(this)">Hapus</button>
                </div>
                <div class="option-group">
                    <input type="text" class="option-input" name="options[]" placeholder="Pilihan 2" value="Pilihan 2">
                    <button type="button" class="remove-option-btn" onclick="removeOption(this)">Hapus</button>
                </div>
                <div class="option-group">
                    <input type="text" class="option-input" name="options[]" placeholder="Pilihan 3" value="Pilihan 3">
                    <button type="button" class="remove-option-btn" onclick="removeOption(this)">Hapus</button>
                </div>
                <div class="option-group">
                    <input type="text" class="option-input" name="options[]" placeholder="Pilihan 4" value="Pilihan 4">
                    <button type="button" class="remove-option-btn" onclick="removeOption(this)">Hapus</button>
                </div>
            </div>
            <button type="button" class="add-option-btn" onclick="addOption()">+ Tambah Pilihan</button>
        </form>

        <div class="spinner-container">
            <div class="spinner-pointer"></div>
            <div class="spinner" id="spinner">
                <!-- Segments akan dibuat dengan JavaScript -->
            </div>
        </div>

        <button class="spin-btn" id="spinBtn" onclick="spinWheel()">🎲 PUTAR SPINNER!</button>

        <div class="result" id="result">
            <h3>Hasil:</h3>
            <div class="result-text" id="resultText"></div>
        </div>
    </div>

    <script>
        let isSpinning = false;
        let currentOptions = [];

        function updateSpinner() {
            const spinner = document.getElementById('spinner');
            const inputs = document.querySelectorAll('.option-input');
            const options = [];
            
            inputs.forEach(input => {
                const value = input.value.trim();
                if (value) {
                    options.push(value);
                }
            });

            if (options.length === 0) {
                options.push('Pilihan 1', 'Pilihan 2', 'Pilihan 3', 'Pilihan 4');
            }

            currentOptions = options;
            const segmentAngle = 360 / options.length;
            
            spinner.innerHTML = '';
            
            options.forEach((option, index) => {
                const segment = document.createElement('div');
                segment.className = 'spinner-segment';
                segment.textContent = option;
                
                const hue = (index * 360 / options.length) % 360;
                segment.style.background = `hsl(${hue}, 70%, 50%)`;
                
                const rotation = index * segmentAngle;
                segment.style.transform = `rotate(${rotation}deg) skew(${90 - segmentAngle}deg)`;
                
                spinner.appendChild(segment);
            });
        }

        function addOption() {
            const container = document.getElementById('optionsContainer');
            const optionCount = container.children.length + 1;
            
            const optionGroup = document.createElement('div');
            optionGroup.className = 'option-group';
            optionGroup.innerHTML = `
                <input type="text" class="option-input" name="options[]" placeholder="Pilihan ${optionCount}">
                <button type="button" class="remove-option-btn" onclick="removeOption(this)">Hapus</button>
            `;
            
            container.appendChild(optionGroup);
            updateSpinner();
        }

        function removeOption(button) {
            const container = document.getElementById('optionsContainer');
            if (container.children.length > 1) {
                button.parentElement.remove();
                updateSpinner();
            } else {
                alert('Minimal harus ada satu pilihan!');
            }
        }

        function spinWheel() {
            if (isSpinning) return;
            
            isSpinning = true;
            const spinBtn = document.getElementById('spinBtn');
            const result = document.getElementById('result');
            const spinner = document.getElementById('spinner');
            
            spinBtn.disabled = true;
            spinBtn.textContent = '🔄 Sedang Berputar...';
            result.classList.remove('show');
            
            // Update options sebelum spin
            updateSpinner();
            
            // Kirim request ke server
            const formData = new FormData();
            currentOptions.forEach(option => {
                formData.append('options[]', option);
            });
            
            fetch('<?= base_url('spinner/spin') ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hitung rotasi berdasarkan hasil
                    const segmentAngle = 360 / data.options.length;
                    const targetAngle = (data.selectedIndex * segmentAngle) + (segmentAngle / 2);
                    const finalRotation = 1800 + (360 - targetAngle); // 5 putaran + posisi target
                    
                    // Animasi spinner
                    spinner.style.transform = `rotate(${finalRotation}deg)`;
                    
                    // Tampilkan hasil setelah animasi selesai
                    setTimeout(() => {
                        document.getElementById('resultText').textContent = data.result;
                        result.classList.add('show');
                        
                        spinBtn.disabled = false;
                        spinBtn.textContent = '🎲 PUTAR LAGI!';
                        isSpinning = false;
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                spinBtn.disabled = false;
                spinBtn.textContent = '🎲 PUTAR SPINNER!';
                isSpinning = false;
            });
        }

        // Event listener untuk update spinner saat input berubah
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('option-input')) {
                updateSpinner();
            }
        });

        // Inisialisasi spinner saat halaman dimuat
        document.addEventListener('DOMContentLoaded', function() {
            updateSpinner();
        });
    </script>
</body>
</html>
