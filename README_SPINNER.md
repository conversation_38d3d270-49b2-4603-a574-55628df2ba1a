# 🎯 Aplikasi Spinner

Aplikasi spinner interaktif yang memungkinkan pengguna untuk memasukkan pilihan secara manual dan memutar spinner untuk mendapatkan hasil acak.

## ✨ Fitur

- **Input Manual**: Pengguna dapat menambah, mengedit, dan menghapus pilihan secara manual
- **Spinner Animasi**: Animasi spinner yang menarik dengan efek rotasi 3D
- **Responsive Design**: <PERSON><PERSON><PERSON> yang responsif dan menarik di berbagai perangkat
- **Warna Dinamis**: Setiap segmen spinner memiliki warna yang berbeda secara otomatis
- **Hasil Real-time**: Menampilkan hasil pilihan secara real-time setelah spinner berhenti

## 🚀 Cara Menggunakan

1. **Akses Aplikasi**: Buka browser dan kunjungi `http://localhost:8080` atau `http://localhost:8080/spinner`

2. **Menambah Pilihan**: 
   - Klik tombol "+ <PERSON><PERSON>" untuk menambah input baru
   - Masukkan teks pilihan yang diinginkan

3. **Mengedit Pilihan**:
   - Klik pada input field dan edit teks sesuai keinginan
   - Spinner akan otomatis update saat Anda mengetik

4. **Menghapus Pilihan**:
   - Klik tombol "Hapus" di sebelah input yang ingin dihapus
   - Minimal harus ada satu pilihan

5. **Memutar Spinner**:
   - Klik tombol "🎲 PUTAR SPINNER!" untuk memulai
   - Tunggu hingga animasi selesai untuk melihat hasil

## 🛠️ Teknologi yang Digunakan

- **Backend**: CodeIgniter 4 (PHP Framework)
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Styling**: CSS Grid, Flexbox, CSS Animations
- **AJAX**: Fetch API untuk komunikasi client-server

## 📁 Struktur File

```
app/
├── Controllers/
│   └── Spinner.php          # Controller utama untuk logika spinner
├── Views/
│   └── spinner/
│       └── index.php        # Tampilan utama aplikasi
└── Config/
    └── Routes.php           # Konfigurasi routing
```

## 🎨 Fitur Desain

- **Gradient Background**: Latar belakang dengan efek gradient yang menarik
- **Card Design**: Tampilan card dengan shadow dan border radius
- **Smooth Animations**: Animasi yang halus menggunakan CSS transitions
- **Color Palette**: Palet warna yang harmonis dan eye-catching
- **Typography**: Font yang mudah dibaca dengan hierarki yang jelas

## 🔧 Konfigurasi

### Routes
```php
$routes->get('/', 'Spinner::index');
$routes->get('spinner', 'Spinner::index');
$routes->post('spinner/spin', 'Spinner::spin');
```

### Controller Methods
- `index()`: Menampilkan halaman utama spinner
- `spin()`: Memproses request untuk memutar spinner dan mengembalikan hasil

## 🎯 Cara Kerja

1. **Input Processing**: JavaScript mengumpulkan semua input pilihan dari form
2. **AJAX Request**: Data dikirim ke server menggunakan Fetch API
3. **Server Processing**: Controller memproses pilihan dan memilih hasil secara acak
4. **Animation**: Spinner berputar dengan animasi CSS berdasarkan hasil dari server
5. **Result Display**: Hasil ditampilkan setelah animasi selesai

## 🚀 Menjalankan Aplikasi

1. Pastikan PHP 8.1+ terinstall
2. Jalankan server development:
   ```bash
   php -S localhost:8080
   ```
3. Buka browser dan akses `http://localhost:8080`

## 🎮 Tips Penggunaan

- Gunakan nama pilihan yang singkat agar mudah dibaca di spinner
- Maksimal 12 pilihan untuk tampilan optimal
- Pilihan kosong akan diabaikan secara otomatis
- Aplikasi akan menggunakan pilihan default jika tidak ada input

## 🔮 Pengembangan Selanjutnya

- Simpan riwayat hasil spinner
- Export hasil ke file
- Tema warna yang dapat disesuaikan
- Sound effects untuk spinner
- Mode fullscreen
- Sharing hasil ke media sosial
